<template>
  <!--
    组件：付款审批-审批页面
    路径：src\views\work\process\mach\flowContract\flowApvContractPayInfo\flowApvContractPayInfoAudit.vue
  -->
  <basic-container>
    <el-tabs v-model="flowTabSelected">
      <el-tab-pane label="流程信息" name="flow">
        <el-row>
          <el-col :span="24">
            <histoicFlowVue :processInstanceId="processInstanceId" :taskId="taskId" />
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="流程图" name="diagram">
        <el-row>
          <el-col :span="24">
            <flowDiagramVue :processInstanceId="processInstanceId" />
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="表单信息" name="form">
        <el-row>
          <el-col :span="24">
            <mach-card title="付款审批单">
              <template #extra>
                <el-button type="primary" icon="el-icon-printer" @click="handlePrint">打印</el-button>
              </template>
              <avue-form
                :option="payinfoOption"
                v-model="form.flowApvContractPayInfo"
                ref="payInfoForm"
                disabled
              >
                <!-- 隐藏avue-form自带的操作按钮 -->
                <template #menu-form="{}">
                  <!-- 不显示任何内容，隐藏清空和提交按钮 -->
                </template>
              </avue-form>

              <el-divider content-position="left">支付方式</el-divider>

              <avue-crud
                :option="paymentOption"
                :data="form.flowApvContractPayInfo.payDetailList"
                ref="crud"
              >
                <!-- 列表：支付金额 -->
                <template #amount="{row}">
                  <span>￥{{ formatAmount(row.amount) }}</span>
                </template>
              </avue-crud>
            </mach-card>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="PDF预览" name="pdf">
        <el-row>
          <el-col :span="24">
            <div v-if="pdfLoading" style="text-align: center; padding: 50px;">
              <el-icon class="is-loading"><Loading /></el-icon>
              <p>正在生成PDF预览...</p>
            </div>
            <div v-else-if="pdfError" style="text-align: center; padding: 50px;">
              <el-icon><Warning /></el-icon>
              <p>PDF预览加载失败</p>
            </div>
            <machPdf v-else-if="pdfSrc" :src="pdfSrc" />
            <div v-else style="text-align: center; padding: 50px;">
              <el-icon><Document /></el-icon>
              <p>暂无PDF预览</p>
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>

    <el-divider content-position="left">审批意见</el-divider>

    <avue-form :option="auditOption" v-model="auditForm" ref="auditFormRef">
      <!-- 隐藏avue-form自带的操作按钮 -->
      <template #menu-form="{}">
        <!-- 不显示任何内容，隐藏清空和提交按钮 -->
      </template>
    </avue-form>

    <el-row style="margin:10px 0 0 0;justify-content: center;">
      <el-button type="primary" icon="Check" @click="handleSubmit" :loading="submitLoading">提交</el-button>
    </el-row>
  </basic-container>
</template>

<script setup>
  import { ref, getCurrentInstance, computed, onMounted } from 'vue';
  import { useStore } from 'vuex';
  import { Loading, Warning, Document } from '@element-plus/icons-vue';
  import histoicFlowVue from '@/views/work/process/mach/histoicFlow/histoicFlow.vue';
  import flowDiagramVue from '@/views/work/process/mach/flow-diagram/flow-diagram.vue';
  import machPdf from "@/components/mach/pdf/mach-pdf.vue";
  import MachCard from "@/components/mach/card/mach-card.vue";
  import { getToken } from '@/utils/auth';
  import { auditFormDetail, audit } from "@/api/mach/pm/flowApvContractPayInfo/apvContractPayInfo";
  import { moneyInterval } from "@/utils/util";

  // 获取this
  let { proxy } = getCurrentInstance()

  // 属性
  const props = defineProps({
    taskId:{},
    processInstanceId:{},
    businessId:{},
    processDefinitionId:{},
    taskDefinitionKey:{},
    status:{},
    taskName:{},
    param:{},
  });

  const store = useStore();
  const userInfo = computed(() => store.state.user.userInfo);
  const permission = computed(() => store.state.user.permission);

  const flowTabSelected = ref("flow");
  const submitLoading = ref(false);
  const auditFormRef = ref(null);
  const pdfLoading = ref(false);
  const pdfError = ref(false);
  const pdfSrc = ref("");
  const form = ref({});
  const auditOption = ref({});
  const auditForm = ref({
    comment: "",
    flag: "1",
    backTaskKey: ""
  });

  // 付款单-表单
  const payinfoOption = ref({
    column: [
      {
        label: "基本信息",
        prop: "baseInfo",
        icon: "el-icon-info",
        collapse: true,
        column: [
          {
            label: "编号",
            prop: "code",
            span: 8
          },
          {
            label: "甲方",
            prop: "partyA",
            type: "select",
            span: 8,
            dicData: [
              {
                label: "北京",
                value: "3"
              },
              {
                label: "江苏",
                value: "2"
              },
              {
                label: "天津",
                value: "1"
              }
            ]
          },
          {
            label: "收款单位",
            prop: "payee",
            span: 8
          },
          {
            label: "单位简称",
            prop: "payeeSimple",
            span: 8
          },
          {
            label: "紧急程度",
            prop: "level",
            type: "select",
            span: 8,
            dicData: [
              {
                label: "一般",
                value: "1"
              },
              {
                label: "紧急",
                value: "2"
              },
              {
                label: "特急",
                value: "3"
              }
            ]
          },
          {
            label: "款项用途",
            prop: "fundsUse",
            span: 8
          },
          {
            label: "支出说明",
            prop: "payDesc",
            span: 24
          },
          {
            label: "付款金额",
            prop: "amount",
            span: 8,
            formatter: (val) => {
              return "￥" + formatAmount(val);
            }
          }
        ]
      },
      {
        label: "银行信息",
        prop: "bankInfo",
        icon: "el-icon-bank-card",
        collapse: true,
        column: [
          {
            label: "银行信息是否变更",
            prop: "bankChange",
            type: "select",
            span: 8,
            dicData: [
              {
                label: "否",
                value: "0"
              },
              {
                label: "是",
                value: "1"
              }
            ]
          },
          {
            label: "开户行",
            prop: "bankAddress",
            span: 8
          },
          {
            label: "账号",
            prop: "bankAccount",
            span: 8
          }
        ]
      },
      {
        label: "其他信息",
        prop: "otherInfo",
        icon: "el-icon-document",
        collapse: true,
        column: [
          {
            label: "是否开具发票",
            prop: "invoiceStatus",
            type: "select",
            span: 8,
            dicData: [
              {
                label: "否",
                value: "0"
              },
              {
                label: "是",
                value: "1"
              }
            ]
          },
          {
            label: "发票备注",
            prop: "invoiceRemark",
            span: 16
          },
          {
            label: "货物是否入库",
            prop: "instoreStatus",
            type: "select",
            span: 8,
            dicData: [
              {
                label: "否",
                value: "0"
              },
              {
                label: "是",
                value: "1"
              }
            ]
          },
          {
            label: "入库备注",
            prop: "instoreRemark",
            span: 16
          },
          {
            label: "支付方式说明",
            prop: "paytypeDesc",
            span: 24
          },
          {
            label: "备注信息",
            prop: "remarks",
            span: 24
          }
        ]
      }
    ]
  });

  // 支付方式-列表
  const paymentOption = ref({
    height: 'auto',
    calcHeight: 30,
    searchShow: false,
    border: true,
    index: true,
    selection: false,
    viewBtn: false,
    editBtn: false,
    delBtn: false,
    addBtn: false,
    menu: false,
    column: [
      {
        label: "支付方式",
        prop: "payType",
        type: "select",
        dicData: [
          {
            label: "现金",
            value: "1"
          },
          {
            label: "银行转账",
            value: "2"
          },
          {
            label: "支票",
            value: "3"
          }
        ]
      },
      {
        label: "金额",
        prop: "amount",
        type: "number",
        precision: 2
      },
      {
        label: "备注",
        prop: "remark"
      }
    ]
  });

  function formatAmount(a){
    return moneyInterval(a);
  }

  // 打印
  function handlePrint() {
    window.print();
  }

  // 生成PDF预览
  function generatePdfPreview() {
    if (!form.value.flowApvContractPayInfo || !form.value.flowApvContractPayInfo.id) {
      pdfError.value = true;
      return;
    }

    pdfLoading.value = true;
    pdfError.value = false;

    // 构建PDF预览URL
    const pdfUrl = `/blade-apvContractPayInfo/apvContractPayInfo/viewApvPdf/${form.value.flowApvContractPayInfo.id}?token=${getToken()}`;
    pdfSrc.value = pdfUrl;

    // 模拟加载完成
    setTimeout(() => {
      pdfLoading.value = false;
    }, 1000);
  }

  // 审核提交
  async function handleSubmit() {
    auditFormRef.value.clearValidate();

    // form本身校验
    let formValidRes = await new Promise((resolve, reject) =>{
        auditFormRef.value.validate((valid, done, msg)=>{
            resolve(valid);
            done();
        })
    });

    if(formValidRes == false){
        proxy.$Message.warning("请完成校验项");
        return false;
    }

    let params = Object.assign(form.value.flowApvContractPayInfo, {
        flow: {
            taskId: props.taskId,
            processInstanceId: props.processInstanceId,
            businessId: props.businessId,
            processDefinitionId: props.processDefinitionId,
            taskDefinitionKey: props.taskDefinitionKey,
            status: props.status,
            flag: auditForm.value.flag,
            comment: auditForm.value.comment,
        },
        backTaskKey: auditForm.value.backTaskKey,
    });

    submitLoading.value = true;
    audit(params).then(resp => {
      submitLoading.value = false;
      proxy.$Message({message: resp.data.msg, type: "success"});
      proxy.$router.push("/work/process");
    }).catch(() => {
      submitLoading.value = false;
      proxy.$Message({message: "提交异常，请联系管理员", type: "error"});
    });
  }

  function init() {
    // 获取审批表单配置
    auditOption.value = {
      column: [
        {
          label: "审批结果",
          prop: "flag",
          type: "radio",
          dicData: [
            {
              label: "同意",
              value: "1"
            },
            {
              label: "驳回",
              value: "0"
            }
          ],
          rules: [
            {
              required: true,
              message: "请选择审批结果",
              trigger: "blur"
            }
          ],
          change: (val) => {
            let backTaskKeyOpt = proxy.findObject(auditOption.value.column, "backTaskKey");
            if (backTaskKeyOpt) {
              backTaskKeyOpt.display = val === "0";
            }
          }
        },
        {
          label: "驳回节点",
          prop: "backTaskKey",
          type: "select",
          display: false,
          dicData: [
            {
              label: "发起人",
              value: "apply_edit"
            }
          ],
          rules: [
            {
              required: true,
              message: "请选择驳回节点",
              trigger: "blur"
            }
          ]
        },
        {
          label: "审批意见",
          prop: "comment",
          type: "textarea",
          span: 24,
          minRows: 3,
          maxRows: 5,
          rules: [
            {
              required: true,
              message: "请输入审批意见",
              trigger: "blur"
            }
          ]
        }
      ]
    };

    // 获取表单数据
    let flow = {
      taskId: props.taskId,
      processInstanceId: props.processInstanceId,
      businessId: props.businessId
    };

    auditFormDetail(flow).then(resp => {
      form.value = resp.data.data;

      // 生成PDF预览
      generatePdfPreview();
    });
  }

  onMounted(() => {
    init();
  });
</script>

<style lang="scss" scoped>
</style>
