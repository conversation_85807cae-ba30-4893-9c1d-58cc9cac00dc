<template>
  <!--
    组件：付款审批-重新编辑页面
    路径：src\views\work\process\mach\flowContract\flowApvContractPayInfo\flowApvContractPayInfoReEdit.vue
  -->
  <basic-container>
    <el-tabs v-model="flowTabSelected">
      <el-tab-pane label="流程信息" name="flow">
        <el-row>
          <el-col :span="24">
            <histoicFlowVue :processInstanceId="processInstanceId" :taskId="taskId" />
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="流程图" name="diagram">
        <el-row>
          <el-col :span="24">
            <flowDiagramVue :processInstanceId="processInstanceId" />
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="表单信息" name="form">
        <el-row>
          <el-col :span="24">
            <mach-card title="付款审批单">
              <apvContractPayInfoForm :flowApvContractPayInfo="form.flowApvContractPayInfo" ref="payInfoFormRef" />
            </mach-card>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="PDF预览" name="pdf">
        <el-row>
          <el-col :span="24">
            <machPdf :processInstanceId="processInstanceId" />
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>

    <el-divider content-position="left">审批意见</el-divider>

    <avue-form :option="auditOption" v-model="auditForm" ref="auditFormRef">
    </avue-form>

    <el-row style="margin:10px 0 0 0;justify-content: center;">
      <el-button type="primary" icon="Check" @click="handleSubmit" :loading="submitLoading">提交</el-button>
    </el-row>
  </basic-container>
</template>

<script setup>
  import { ref, getCurrentInstance, computed, toRefs, onMounted } from 'vue';
  import { useStore } from 'vuex';
  import histoicFlowVue from '@/views/work/process/mach/histoicFlow/histoicFlow.vue';
  import flowDiagramVue from '@/views/work/process/mach/flow-diagram/flow-diagram.vue';
  import mach from "@/utils/mach";
  import machPdf from "@/components/mach/pdf/mach-pdf.vue";
  import MachCard from "@/components/mach/card/mach-card.vue";
  import { getToken } from '@/utils/auth';
  import { auditFormDetail, audit } from "@/api/mach/pm/flowApvContractPayInfo/apvContractPayInfo";
  import apvContractPayInfoForm from "@/views/mach/pm/flowContract/flowApvContractPayInfo/apvContractPayInfo-form.vue";

  // 获取this
  let { proxy } = getCurrentInstance()

  // 属性
  const props = defineProps({
    taskId:{},
    processInstanceId:{},
    businessId:{},
    processDefinitionId:{},
    taskDefinitionKey:{},
    status:{},
    taskName:{},
    param:{},
  });

  const store = useStore();
  const userInfo = computed(() => store.state.user.userInfo);
  const permission = computed(() => store.state.user.permission);

  const flowTabSelected = ref("flow");
  const submitLoading = ref(false);
  const auditFormRef = ref(null);
  const form = ref({});
  const auditOption = ref({});
  const auditForm = ref({
    comment: "",
    flag: "1"
  });

  // 审核提交
  async function handleSubmit() {
    // 表单校验
    let formValidRes = await proxy.$refs.payInfoFormRef.validForm();

    if (!formValidRes.pass) {
      proxy.$Message.warning(formValidRes.msg);
      return false;
    }

    // 审批意见校验
    let auditValidRes = await new Promise((resolve, reject) => {
      proxy.$refs.auditFormRef.validate((valid, done, msg) => {
        resolve(valid);
        done();
      });
    });

    if (!auditValidRes) {
      proxy.$Message.warning("请完成审批意见");
      return false;
    }

    // 获取表单数据
    let payInfoData = formValidRes.data;

    let params = Object.assign({}, payInfoData, {
      flow: {
        taskId: props.taskId,
        processInstanceId: props.processInstanceId,
        businessId: props.businessId,
        processDefinitionId: props.processDefinitionId,
        taskDefinitionKey: props.taskDefinitionKey,
        status: props.status,
        flag: auditForm.value.flag,
        comment: auditForm.value.comment,
      }
    });

    submitLoading.value = true;
    audit(params).then(resp => {
      submitLoading.value = false;
      proxy.$Message({message: resp.data.msg, type: "success"});
      proxy.$router.push("/work/process");
    }).catch(() => {
      submitLoading.value = false;
      proxy.$Message({message: "提交异常，请联系管理员", type: "error"});
    });
  }

  function init() {
    // 获取审批表单配置
    auditOption.value = {
      column: [
        {
          label: "审批意见",
          prop: "comment",
          type: "textarea",
          span: 24,
          minRows: 3,
          maxRows: 5,
          rules: [
            {
              required: true,
              message: "请输入审批意见",
              trigger: "blur"
            }
          ]
        }
      ]
    };

    // 获取表单数据
    let flow = {
      taskId: props.taskId,
      processInstanceId: props.processInstanceId,
      businessId: props.businessId
    };

    auditFormDetail(flow).then(resp => {
      form.value = resp.data.data;
    });
  }

  onMounted(() => {
    init();
  });
</script>

<style lang="scss" scoped>
</style>
